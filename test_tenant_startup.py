#!/usr/bin/env python3
"""
Test script to verify tenant system startup without errors
"""
import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def load_env_file():
    """Load environment variables from .localenv"""
    env_file = project_root / '.localenv'
    if env_file.exists():
        print(f"Loading environment from: {env_file}")
        with open(env_file) as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value
    else:
        print("Warning: .localenv file not found")

def test_prometheus_metrics():
    """Test that Prometheus metrics can be initialized multiple times"""
    print("Testing Prometheus metrics initialization...")
    
    try:
        # Load environment first
        load_env_file()
        
        # Import the config module multiple times to simulate multiple initializations
        for i in range(3):
            print(f"  Initialization attempt {i+1}...")
            
            # Clear the module cache to force re-import
            if 'superset.superset_config' in sys.modules:
                del sys.modules['superset.superset_config']
            
            # Import the config
            from superset.superset_config import SUP_QUERY_TOTAL, SUP_QUERY_DURATION
            
            # Test that metrics work
            if hasattr(SUP_QUERY_TOTAL, 'inc'):
                print(f"    ✅ SUP_QUERY_TOTAL is functional")
            else:
                print(f"    ⚠️  SUP_QUERY_TOTAL is dummy metric")
            
            if hasattr(SUP_QUERY_DURATION, 'observe'):
                print(f"    ✅ SUP_QUERY_DURATION is functional")
            else:
                print(f"    ⚠️  SUP_QUERY_DURATION is dummy metric")
        
        print("✅ Prometheus metrics test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error testing Prometheus metrics: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tenant_manager_import():
    """Test that tenant manager can be imported without errors"""
    print("\nTesting tenant manager import...")
    
    try:
        from superset.tenant_manager import tenant_manager
        
        # Test basic functionality
        all_tenants = tenant_manager.get_all_tenants()
        print(f"  Current tenants: {all_tenants}")
        
        # Test URL extraction
        test_url = "/tenant/test123/dashboard"
        extracted = tenant_manager.extract_tenant_from_url(test_url)
        print(f"  URL extraction test: {test_url} -> {extracted}")
        
        print("✅ Tenant manager import test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error testing tenant manager: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tenant_database_manager():
    """Test tenant database manager initialization"""
    print("\nTesting tenant database manager...")
    
    try:
        from superset.utils.tenant_database_manager import initialize_tenant_databases, _tenant_databases_initialized
        
        print(f"  Initial state: initialized = {_tenant_databases_initialized}")
        
        # Test multiple initialization calls
        for i in range(3):
            print(f"  Initialization call {i+1}...")
            try:
                initialize_tenant_databases()
                print(f"    ✅ Call {i+1} completed")
            except Exception as e:
                print(f"    ❌ Call {i+1} failed: {e}")
        
        print("✅ Tenant database manager test completed")
        return True
        
    except Exception as e:
        print(f"❌ Error testing tenant database manager: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Tenant System Startup Test")
    print("=" * 50)
    
    success = True
    success &= test_prometheus_metrics()
    success &= test_tenant_manager_import()
    success &= test_tenant_database_manager()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All startup tests passed!")
        print("\nThe tenant system should now start without Prometheus errors.")
        print("You can now test with:")
        print("  docker-compose up superset-node")
    else:
        print("❌ Some startup tests failed!")
        sys.exit(1)
