#!/usr/bin/env python3
"""
Test script to verify tenant configuration
"""
import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_tenant_config():
    """Test tenant configuration loading"""
    print("Testing tenant configuration...")

    # Load environment variables from .localenv
    env_file = project_root / '.localenv'
    if env_file.exists():
        print(f"Loading environment from: {env_file}")
        with open(env_file) as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value
    else:
        print("Warning: .localenv file not found")

    # Test tenant configuration directly
    try:
        # Import the function directly to avoid full app import
        sys.path.insert(0, str(project_root / 'superset'))

        # Manually implement get_env_variable for testing
        def get_env_variable(var_name, default=None):
            value = os.environ.get(var_name, default)
            if value is None and default is None:
                raise EnvironmentError(f"The environment variable {var_name} is missing")
            return value

        # Test tenant database configuration
        tenant_databases = {}
        tenant_ids = get_env_variable("TENANT_IDS", "").split(",")

        for tenant_id in tenant_ids:
            if not tenant_id.strip():
                continue

            tenant_id = tenant_id.strip()
            try:
                # Get tenant-specific database configuration
                tenant_dialect = get_env_variable(f"TENANT_{tenant_id.upper()}_DIALECT")
                tenant_user = get_env_variable(f"TENANT_{tenant_id.upper()}_USER")
                tenant_password = get_env_variable(f"TENANT_{tenant_id.upper()}_PASSWORD")
                tenant_host = get_env_variable(f"TENANT_{tenant_id.upper()}_HOST")
                tenant_port = get_env_variable(f"TENANT_{tenant_id.upper()}_PORT")
                tenant_db = get_env_variable(f"TENANT_{tenant_id.upper()}_DATABASE")

                # Build connection string
                db_uri = f"{tenant_dialect}://{tenant_user}:{tenant_password}@{tenant_host}:{tenant_port}/{tenant_db}"
                tenant_databases[tenant_id] = db_uri

            except EnvironmentError as e:
                print(f"Skipping tenant {tenant_id}: {e}")
                continue

        print(f"\nConfigured tenants: {len(tenant_databases)}")
        for tenant_id, db_uri in tenant_databases.items():
            # Mask password in output
            masked_uri = db_uri.replace(f":{os.environ.get(f'TENANT_{tenant_id.upper()}_PASSWORD', '')}@", ":***@")
            print(f"  - {tenant_id}: {masked_uri}")

        if not tenant_databases:
            print("No tenant databases configured!")
            return False

        print("\n✅ Tenant configuration loaded successfully")
        return True

    except Exception as e:
        print(f"❌ Error loading tenant configuration: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_url_extraction():
    """Test URL tenant extraction"""
    print("\nTesting URL tenant extraction...")

    try:
        # Test URL extraction without importing full superset
        import re

        def extract_tenant_from_url(url_path: str):
            """Extract tenant ID from URL path like /tenant/{tenant_id}/dashboard"""
            # Pattern: /tenant/{tenant_id}/anything or /tenant/{tenant_id}
            pattern = r'^/tenant/([^/]+)(?:/|$)'
            match = re.match(pattern, url_path)
            return match.group(1) if match else None

        test_urls = [
            "/tenant/tenant1/dashboard",
            "/tenant/tenant2/explore",
            "/tenant/tenant1/",
            "/tenant/tenant1",
            "/dashboard",  # No tenant
            "/tenant/invalid_tenant/dashboard"
        ]

        for url in test_urls:
            tenant_id = extract_tenant_from_url(url)
            print(f"  {url} -> {tenant_id or 'None'}")

        print("\n✅ URL extraction test completed")
        return True

    except Exception as e:
        print(f"❌ Error testing URL extraction: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_connections():
    """Test database connections"""
    print("\nTesting database connections...")

    try:
        from sqlalchemy import create_engine, text

        # Get tenant configuration
        tenant_ids = os.environ.get("TENANT_IDS", "").split(",")

        for tenant_id in tenant_ids:
            if not tenant_id.strip():
                continue

            tenant_id = tenant_id.strip()
            try:
                # Build connection string
                dialect = os.environ.get(f"TENANT_{tenant_id.upper()}_DIALECT")
                user = os.environ.get(f"TENANT_{tenant_id.upper()}_USER")
                password = os.environ.get(f"TENANT_{tenant_id.upper()}_PASSWORD")
                host = os.environ.get(f"TENANT_{tenant_id.upper()}_HOST")
                port = os.environ.get(f"TENANT_{tenant_id.upper()}_PORT")
                database = os.environ.get(f"TENANT_{tenant_id.upper()}_DATABASE")

                if not all([dialect, user, password, host, port, database]):
                    print(f"  - {tenant_id}: ❌ Missing configuration")
                    continue

                db_uri = f"{dialect}://{user}:{password}@{host}:{port}/{database}"

                # Test connection
                engine = create_engine(db_uri, pool_pre_ping=True)
                with engine.connect() as conn:
                    result = conn.execute(text("SELECT 1"))
                    result.fetchone()

                print(f"  - {tenant_id}: ✅ Connection OK")

            except Exception as e:
                print(f"  - {tenant_id}: ❌ Connection failed: {e}")

        print("\n✅ Database connection test completed")
        return True

    except Exception as e:
        print(f"❌ Error testing database connections: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Tenant Configuration Test")
    print("=" * 50)
    
    success = True
    success &= test_tenant_config()
    success &= test_url_extraction()
    success &= test_database_connections()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed!")
    else:
        print("❌ Some tests failed!")
        sys.exit(1)
