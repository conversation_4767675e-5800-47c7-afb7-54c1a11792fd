#!/usr/bin/env python3
"""
Test script to verify tenant URL routing works
"""
import requests
import time
import sys

def test_superset_startup():
    """Test that Superset is running"""
    print("Testing Superset startup...")
    
    max_retries = 30
    for i in range(max_retries):
        try:
            response = requests.get("http://localhost:8088/health", timeout=5)
            if response.status_code == 200:
                print("✅ Superset is running")
                return True
        except requests.exceptions.RequestException:
            pass
        
        print(f"  Waiting for Superset... ({i+1}/{max_retries})")
        time.sleep(2)
    
    print("❌ Superset is not responding")
    return False

def test_tenant_urls():
    """Test tenant URL routing"""
    print("\nTesting tenant URL routing...")
    
    test_urls = [
        ("http://localhost:8088/", "Default URL"),
        ("http://localhost:8088/tenant/tenant1/", "Tenant1 URL"),
        ("http://localhost:8088/tenant/tenant2/", "Tenant2 URL"),
        ("http://localhost:8088/tenant/invalid/", "Invalid tenant URL"),
    ]
    
    results = []
    
    for url, description in test_urls:
        try:
            print(f"  Testing {description}: {url}")
            response = requests.get(url, timeout=10, allow_redirects=False)
            
            status = response.status_code
            if status == 200:
                print(f"    ✅ {status} - OK")
                results.append(True)
            elif status in [301, 302, 307, 308]:
                print(f"    ↗️  {status} - Redirect to: {response.headers.get('Location', 'Unknown')}")
                results.append(True)
            elif status == 404:
                if "invalid" in url:
                    print(f"    ✅ {status} - Expected 404 for invalid tenant")
                    results.append(True)
                else:
                    print(f"    ❌ {status} - Unexpected 404")
                    results.append(False)
            else:
                print(f"    ⚠️  {status} - Unexpected status")
                results.append(False)
                
        except requests.exceptions.RequestException as e:
            print(f"    ❌ Request failed: {e}")
            results.append(False)
    
    success_rate = sum(results) / len(results) * 100
    print(f"\n  Success rate: {success_rate:.1f}% ({sum(results)}/{len(results)})")
    
    return success_rate >= 75  # Allow some failures

def test_login_page():
    """Test that login page loads"""
    print("\nTesting login page...")
    
    try:
        response = requests.get("http://localhost:8088/login/", timeout=10)
        if response.status_code == 200:
            if "login" in response.text.lower() or "username" in response.text.lower():
                print("✅ Login page loads correctly")
                return True
            else:
                print("⚠️  Login page loads but content unexpected")
                return False
        else:
            print(f"❌ Login page returned status: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Login page request failed: {e}")
        return False

def check_logs_for_errors():
    """Check if there are any critical errors in the logs"""
    print("\nChecking for critical errors...")
    print("  (Check Docker logs manually with: docker-compose logs superset-node)")
    print("  Look for:")
    print("    ❌ Any 'ERROR' messages related to tenant system")
    print("    ❌ Any 'Duplicated timeseries' errors")
    print("    ✅ 'Tenant system initialized successfully' messages")
    print("    ✅ 'Configured tenant database: tenant1/tenant2' messages")
    return True

if __name__ == "__main__":
    print("🧪 Tenant URL Routing Test")
    print("=" * 50)
    
    success = True
    
    # Test Superset startup
    if not test_superset_startup():
        print("\n❌ Superset is not running. Start it with:")
        print("  docker-compose up superset-node")
        sys.exit(1)
    
    # Test tenant URLs
    success &= test_tenant_urls()
    
    # Test login page
    success &= test_login_page()
    
    # Check logs
    success &= check_logs_for_errors()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Tenant URL routing tests completed!")
        print("\nNext steps:")
        print("1. Check Docker logs for any errors")
        print("2. Try accessing tenant URLs in browser:")
        print("   - http://localhost:8088/tenant/tenant1/")
        print("   - http://localhost:8088/tenant/tenant2/")
        print("3. Login and verify database switching works")
    else:
        print("❌ Some tests failed!")
        print("Check the output above and Docker logs for details.")
        sys.exit(1)
