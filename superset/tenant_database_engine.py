"""
Tenant-aware database engine for Superset metadata
"""
import logging
from sqlalchemy import create_engine, event
from sqlalchemy.engine import Engine
from sqlalchemy.pool import StaticPool
from flask import g, has_request_context

logger = logging.getLogger(__name__)

class TenantAwareEngine:
    """A wrapper that provides tenant-aware database engines"""
    
    def __init__(self, default_uri):
        self.default_uri = default_uri
        self.engines = {}  # Cache of engines per tenant
        self.default_engine = None
        logger.info(f"Initialized TenantAwareEngine with default URI: {default_uri}")
    
    def get_engine(self):
        """Get the appropriate engine based on current tenant context"""
        try:
            # Only try tenant switching if we're in a request context
            if not has_request_context():
                return self._get_default_engine()
            
            # Try to get tenant context
            from superset.tenant_manager import tenant_manager
            tenant_id = tenant_manager.get_current_tenant()
            
            if tenant_id:
                # Get tenant-specific engine
                tenant_engine = self._get_tenant_engine(tenant_id)
                if tenant_engine:
                    logger.debug(f"🔄 Using tenant metadata database: {tenant_id}")
                    return tenant_engine
            
            # Fallback to default engine
            return self._get_default_engine()
            
        except Exception as e:
            logger.warning(f"Error getting tenant engine, using default: {e}")
            return self._get_default_engine()
    
    def _get_default_engine(self):
        """Get the default engine"""
        if not self.default_engine:
            self.default_engine = create_engine(
                self.default_uri,
                poolclass=StaticPool,
                pool_pre_ping=True,
                pool_recycle=300,
                echo=False
            )
            logger.info(f"Created default engine: {self.default_uri}")
        return self.default_engine
    
    def _get_tenant_engine(self, tenant_id):
        """Get or create engine for specific tenant"""
        if tenant_id not in self.engines:
            try:
                from superset.tenant_manager import tenant_manager
                tenant_uri = tenant_manager.get_tenant_database_uri(tenant_id)
                
                if tenant_uri:
                    self.engines[tenant_id] = create_engine(
                        tenant_uri,
                        poolclass=StaticPool,
                        pool_pre_ping=True,
                        pool_recycle=300,
                        echo=False
                    )
                    logger.info(f"🔄 Created tenant engine: {tenant_id} -> {tenant_uri}")
                else:
                    logger.warning(f"No database URI found for tenant: {tenant_id}")
                    return None
                    
            except Exception as e:
                logger.error(f"Failed to create engine for tenant {tenant_id}: {e}")
                return None
        
        return self.engines.get(tenant_id)
    
    def dispose_all(self):
        """Dispose all engines"""
        if self.default_engine:
            self.default_engine.dispose()
        for engine in self.engines.values():
            engine.dispose()
        self.engines.clear()
        logger.info("Disposed all tenant engines")

# Global tenant-aware engine instance
_tenant_engine = None

def get_tenant_aware_engine(default_uri):
    """Get the global tenant-aware engine instance"""
    global _tenant_engine
    if not _tenant_engine:
        _tenant_engine = TenantAwareEngine(default_uri)
    return _tenant_engine

def create_tenant_aware_engine(uri, **kwargs):
    """Create a tenant-aware engine that switches based on context"""
    logger.info(f"Creating tenant-aware engine with URI: {uri}")
    
    # Get the tenant-aware engine wrapper
    tenant_engine_wrapper = get_tenant_aware_engine(uri)
    
    # Return the appropriate engine based on current context
    return tenant_engine_wrapper.get_engine()

# Hook into SQLAlchemy engine creation
@event.listens_for(Engine, "connect")
def set_tenant_context_on_connect(dbapi_connection, connection_record):
    """Set tenant context information on database connections"""
    try:
        if has_request_context():
            from superset.tenant_manager import tenant_manager
            tenant_id = tenant_manager.get_current_tenant()
            if tenant_id:
                # Store tenant info in connection record for debugging
                connection_record.info['tenant_id'] = tenant_id
                logger.debug(f"Database connection established for tenant: {tenant_id}")
    except Exception as e:
        logger.debug(f"Could not set tenant context on connection: {e}")
