from typing import Dict, Optional
from sqlalchemy import create_engine
from flask import request, g
import logging
import re

class TenantManager:
    """Manages tenant database connections and context"""
    
    def __init__(self):
        self.tenant_databases: Dict[str, str] = {}
        self.engines: Dict[str, any] = {}
        self.logger = logging.getLogger(__name__)
    
    def register_tenant_database(self, tenant_id: str, database_uri: str):
        """Register a database for a tenant"""
        self.tenant_databases[tenant_id] = database_uri
        # Create engine for this tenant
        self.engines[tenant_id] = create_engine(database_uri)
        self.logger.info(f"Registered database for tenant: {tenant_id}")
    
    def extract_tenant_from_url(self, url_path: str) -> Optional[str]:
        """Extract tenant ID from URL path like /tenant/{tenant_id}/dashboard"""
        # Pattern: /tenant/{tenant_id}/anything
        pattern = r'^/tenant/([^/]+)/'
        match = re.match(pattern, url_path)
        return match.group(1) if match else None
    
    def get_current_tenant(self) -> Optional[str]:
        """Get current tenant from request context"""
        # First check if already set in request context
        if hasattr(g, 'current_tenant'):
            return g.current_tenant
        
        # Extract from current request URL
        if request and request.path:
            tenant_id = self.extract_tenant_from_url(request.path)
            if tenant_id:
                self.set_current_tenant(tenant_id)
                return tenant_id
        
        return None
    
    def set_current_tenant(self, tenant_id: str):
        """Set current tenant in request context"""
        g.current_tenant = tenant_id
        self.logger.debug(f"Set current tenant to: {tenant_id}")
    
    def get_tenant_database_uri(self, tenant_id: str) -> Optional[str]:
        """Get database URI for tenant"""
        return self.tenant_databases.get(tenant_id)
    
    def get_tenant_engine(self, tenant_id: str):
        """Get SQLAlchemy engine for tenant"""
        return self.engines.get(tenant_id)
    
    def validate_tenant(self, tenant_id: str) -> bool:
        """Check if tenant exists and is configured"""
        return tenant_id in self.tenant_databases
    
    def get_all_tenants(self) -> list:
        """Get list of all configured tenants"""
        return list(self.tenant_databases.keys())

# Global instance
tenant_manager = TenantManager()