from typing import Dict, Optional
from sqlalchemy import create_engine
from flask import request, g, current_app
import logging
import re

class TenantManager:
    """Manages tenant database connections and context"""

    def __init__(self):
        self.tenant_databases: Dict[str, str] = {}
        self.engines: Dict[str, any] = {}
        self.logger = logging.getLogger(__name__)

    def register_tenant_database(self, tenant_id: str, database_uri: str):
        """Register a database for a tenant"""
        self.tenant_databases[tenant_id] = database_uri
        # Create engine for this tenant with proper configuration
        engine_params = {
            'pool_pre_ping': True,
            'pool_recycle': 300,
            'echo': False
        }
        self.engines[tenant_id] = create_engine(database_uri, **engine_params)
        self.logger.info(f"Registered database for tenant: {tenant_id}")

    def extract_tenant_from_url(self, url_path: str) -> Optional[str]:
        """Extract tenant ID from URL path like /tenant/{tenant_id}/dashboard"""
        # Pattern: /tenant/{tenant_id}/anything or /tenant/{tenant_id}
        pattern = r'^/tenant/([^/]+)(?:/|$)'
        match = re.match(pattern, url_path)
        return match.group(1) if match else None

    def get_current_tenant(self) -> Optional[str]:
        """Get current tenant from request context"""
        # First check if already set in request context
        if hasattr(g, 'current_tenant'):
            self.logger.debug(f"Found tenant in g.current_tenant: {g.current_tenant}")
            return g.current_tenant

        # Extract from current request URL
        if request and request.path:
            tenant_id = self.extract_tenant_from_url(request.path)
            if tenant_id and self.validate_tenant(tenant_id):
                self.set_current_tenant(tenant_id)
                self.logger.debug(f"Found tenant in URL path: {tenant_id}")
                return tenant_id

        # Check session for tenant_id
        try:
            from flask import session
            if session and 'tenant_id' in session:
                tenant_id = session['tenant_id']
                if tenant_id and self.validate_tenant(tenant_id):
                    self.set_current_tenant(tenant_id)
                    self.logger.debug(f"Found tenant in session: {tenant_id}")
                    return tenant_id
        except:
            pass

        # Check request args for tenant parameter
        if request and request.args:
            tenant_id = request.args.get('tenant')
            if tenant_id and self.validate_tenant(tenant_id):
                self.set_current_tenant(tenant_id)
                self.logger.debug(f"Found tenant in request args: {tenant_id}")
                return tenant_id

        # Check for default tenant if configured
        default_tenant = current_app.config.get('DEFAULT_TENANT') if current_app else None
        if default_tenant and self.validate_tenant(default_tenant):
            self.logger.debug(f"Using default tenant: {default_tenant}")
            return default_tenant

        self.logger.debug("No tenant context found")
        return None

    def set_current_tenant(self, tenant_id: str):
        """Set current tenant in request context"""
        if self.validate_tenant(tenant_id):
            g.current_tenant = tenant_id
            self.logger.debug(f"Set current tenant to: {tenant_id}")
        else:
            self.logger.warning(f"Attempted to set invalid tenant: {tenant_id}")
            raise ValueError(f"Invalid tenant: {tenant_id}")

    def get_tenant_database_uri(self, tenant_id: str) -> Optional[str]:
        """Get database URI for tenant"""
        return self.tenant_databases.get(tenant_id)

    def get_tenant_engine(self, tenant_id: str):
        """Get SQLAlchemy engine for tenant"""
        return self.engines.get(tenant_id)

    def get_current_tenant_engine(self):
        """Get SQLAlchemy engine for current tenant"""
        tenant_id = self.get_current_tenant()
        if tenant_id:
            return self.get_tenant_engine(tenant_id)
        return None

    def validate_tenant(self, tenant_id: str) -> bool:
        """Check if tenant exists and is configured"""
        return tenant_id in self.tenant_databases

    def get_all_tenants(self) -> list:
        """Get list of all configured tenants"""
        return list(self.tenant_databases.keys())

    def get_tenant_sqlalchemy_uri(self, tenant_id: Optional[str] = None) -> Optional[str]:
        """Get the SQLAlchemy URI for a tenant (or current tenant)"""
        if tenant_id is None:
            tenant_id = self.get_current_tenant()

        if tenant_id and self.validate_tenant(tenant_id):
            return self.get_tenant_database_uri(tenant_id)

        return None

# Global instance
tenant_manager = TenantManager()