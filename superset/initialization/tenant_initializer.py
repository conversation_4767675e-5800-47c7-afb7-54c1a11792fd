"""
Tenant-aware app initializer that extends the default Superset initializer
"""
import logging
from superset.initialization import SupersetAppInitializer
from superset.extensions import appbuilder

logger = logging.getLogger(__name__)

class TenantAwareAppInitializer(SupersetAppInitializer):
    """App initializer that includes tenant system setup"""
    
    def init_views(self) -> None:
        """Initialize views including tenant views"""
        # Call parent method to initialize all standard views
        super().init_views()
        
        # Add tenant-specific views
        self.init_tenant_views()
    
    def init_tenant_views(self) -> None:
        """Initialize tenant-specific views"""
        try:
            from superset.views.tenant_views import TenantRoutingView
            
            # Add tenant routing view without menu (it's just for routing)
            appbuilder.add_view_no_menu(TenantRoutingView)
            
            logger.info("Tenant routing views registered successfully")
            
        except Exception as e:
            logger.error(f"Failed to register tenant views: {e}")
            # Don't fail app startup for tenant view registration issues
            pass
    
    def post_init(self) -> None:
        """Called after any other init tasks"""
        # Call parent post_init first
        super().post_init()
        
        # Initialize tenant system
        self.init_tenant_system()
    
    def init_tenant_system(self) -> None:
        """Initialize the tenant system"""
        # Only initialize if not already done (prevents duplicate initialization)
        if not hasattr(self.superset_app, '_tenant_system_initialized'):
            try:
                from superset.views.tenant_views import tenant_middleware
                from superset.utils.tenant_database_manager import initialize_tenant_databases
                
                # Initialize tenant middleware
                tenant_middleware.init_app(self.superset_app)
                
                # Initialize tenant databases (safe initialization that won't fail app startup)
                initialize_tenant_databases()
                
                # Mark as initialized to prevent duplicate initialization
                self.superset_app._tenant_system_initialized = True
                logger.info("Tenant system initialized successfully")
                
            except Exception as e:
                logger.error(f"Failed to initialize tenant system: {e}")
                # Don't fail app startup for tenant system issues
                pass
