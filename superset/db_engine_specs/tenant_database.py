from superset.db_engine_specs.base import BaseEngineSpec
from superset.tenant_manager import tenant_manager
from sqlalchemy import create_engine
from flask import g, current_app
import logging

logger = logging.getLogger(__name__)

class TenantDatabaseSpec(BaseEngineSpec):
    """Database spec that switches connection based on tenant"""

    engine = "tenant_database"
    engine_name = "Tenant Database"

    @classmethod
    def get_engine(cls, database, schema=None, source=None):
        """Get engine for current tenant"""
        tenant_id = tenant_manager.get_current_tenant()

        if not tenant_id:
            logger.warning("No tenant context found, using default database")
            # Fall back to default database if no tenant context
            return database._get_sqla_engine(schema=schema, source=source)

        engine = tenant_manager.get_tenant_engine(tenant_id)
        if not engine:
            logger.error(f"No database configured for tenant {tenant_id}")
            raise Exception(f"No database configured for tenant {tenant_id}")

        logger.debug(f"Using tenant database engine for: {tenant_id}")
        return engine

def get_tenant_aware_sqlalchemy_uri():
    """Get SQLAlchemy URI based on current tenant context"""
    tenant_id = tenant_manager.get_current_tenant()

    if tenant_id:
        tenant_uri = tenant_manager.get_tenant_database_uri(tenant_id)
        if tenant_uri:
            logger.debug(f"Using tenant database URI for: {tenant_id}")
            return tenant_uri

    # Fall back to default URI
    default_uri = current_app.config.get('SQLALCHEMY_DATABASE_URI')
    logger.debug("Using default database URI")
    return default_uri