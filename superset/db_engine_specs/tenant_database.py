from superset.db_engine_specs.base import BaseEngineSpec
from superset.tenant_manager import tenant_manager
from sqlalchemy import create_engine
from flask import g
import logging

class TenantDatabaseSpec(BaseEngineSpec):
    """Database spec that switches connection based on tenant"""
    
    engine = "tenant_database"
    engine_name = "Tenant Database"
    
    @classmethod
    def get_engine(cls, database, schema=None, source=None):
        """Get engine for current tenant"""
        tenant_id = tenant_manager.get_current_tenant()
        
        if not tenant_id:
            raise Exception("No tenant context found")
        
        engine = tenant_manager.get_tenant_engine(tenant_id)
        if not engine:
            raise Exception(f"No database configured for tenant {tenant_id}")
        
        return engine