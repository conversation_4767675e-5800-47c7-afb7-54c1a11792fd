"""
Tenant Database Manager - Handles registration and management of tenant databases
"""
import logging
from typing import Dict, List, Optional
from flask import current_app
from superset import db
from superset.models.core import Database
from superset.tenant_manager import tenant_manager

logger = logging.getLogger(__name__)

class TenantDatabaseManager:
    """Manages tenant database registration in Superset"""
    
    def __init__(self):
        self.registered_tenants: Dict[str, int] = {}  # tenant_id -> database_id mapping
        self.logger = logging.getLogger(__name__)
    
    def register_tenant_databases(self, tenant_databases: Dict[str, str]) -> None:
        """
        Register tenant databases as Database objects in Superset
        
        Args:
            tenant_databases: Dict mapping tenant_id to database_uri
        """
        for tenant_id, database_uri in tenant_databases.items():
            try:
                self._register_single_tenant_database(tenant_id, database_uri)
            except Exception as e:
                self.logger.error(f"Failed to register tenant database {tenant_id}: {e}")
    
    def _register_single_tenant_database(self, tenant_id: str, database_uri: str) -> None:
        """Register a single tenant database"""
        from flask import current_app

        # Skip database registration if no app context
        if not current_app:
            self.logger.warning(f"No application context for tenant database registration: {tenant_id}")
            return

        database_name = f"tenant_{tenant_id}"

        try:
            # Check if database already exists
            existing_db = db.session.query(Database).filter_by(database_name=database_name).first()

            if existing_db:
                # Update existing database URI if different
                if existing_db.sqlalchemy_uri_decrypted != database_uri:
                    existing_db.set_sqlalchemy_uri(database_uri)
                    db.session.commit()
                    self.logger.info(f"Updated database URI for tenant: {tenant_id}")

                self.registered_tenants[tenant_id] = existing_db.id
            else:
                # Create new database entry
                new_db = Database(
                    database_name=database_name,
                    verbose_name=f"Tenant {tenant_id} Database",
                    expose_in_sqllab=True,
                    allow_run_async=True,
                    allow_ctas=True,
                    allow_cvas=True,
                    allow_dml=True
                )
                new_db.set_sqlalchemy_uri(database_uri)

                db.session.add(new_db)
                db.session.commit()

                self.registered_tenants[tenant_id] = new_db.id
                self.logger.info(f"Registered new database for tenant: {tenant_id}")

        except Exception as e:
            self.logger.error(f"Failed to register database for tenant {tenant_id}: {e}")
            # Don't raise the exception, just log it
    
    def get_tenant_database_id(self, tenant_id: str) -> Optional[int]:
        """Get the database ID for a tenant"""
        return self.registered_tenants.get(tenant_id)
    
    def get_tenant_database(self, tenant_id: str) -> Optional[Database]:
        """Get the Database object for a tenant"""
        db_id = self.get_tenant_database_id(tenant_id)
        if db_id:
            return db.session.query(Database).get(db_id)
        return None
    
    def validate_tenant_databases(self) -> List[str]:
        """
        Validate that all registered tenant databases are accessible
        Returns list of tenant_ids with connection issues
        """
        failed_tenants = []
        
        for tenant_id in self.registered_tenants.keys():
            try:
                tenant_db = self.get_tenant_database(tenant_id)
                if tenant_db:
                    # Test connection
                    with tenant_db.get_sqla_engine_with_context() as engine:
                        with engine.connect() as conn:
                            from sqlalchemy import text
                            conn.execute(text("SELECT 1"))
                    self.logger.debug(f"Tenant database connection OK: {tenant_id}")
                else:
                    failed_tenants.append(tenant_id)
                    self.logger.error(f"Tenant database not found: {tenant_id}")
            except Exception as e:
                failed_tenants.append(tenant_id)
                self.logger.error(f"Tenant database connection failed for {tenant_id}: {e}")
        
        return failed_tenants
    
    def get_all_registered_tenants(self) -> List[str]:
        """Get list of all registered tenant IDs"""
        return list(self.registered_tenants.keys())

# Global instance
tenant_database_manager = TenantDatabaseManager()

# Flag to prevent multiple initialization
_tenant_databases_initialized = False

def initialize_tenant_databases():
    """Initialize tenant databases from configuration"""
    global _tenant_databases_initialized

    # Prevent multiple initialization
    if _tenant_databases_initialized:
        logger.debug("Tenant databases already initialized, skipping")
        return

    try:
        from superset.superset_config import get_tenant_databases

        # Get tenant configuration
        tenant_databases = get_tenant_databases()

        if not tenant_databases:
            logger.warning("No tenant databases configured")
            return

        # Register tenant databases in tenant manager (this is the core functionality)
        for tenant_id, db_uri in tenant_databases.items():
            tenant_manager.register_tenant_database(tenant_id, db_uri)

        # Skip Database object registration during startup to avoid app context issues
        # The DB_CONNECTION_MUTATOR will handle database switching at runtime
        logger.info(f"Successfully initialized tenant databases: {list(tenant_databases.keys())}")
        _tenant_databases_initialized = True

    except Exception as e:
        logger.error(f"Failed to initialize tenant databases: {e}")
        # Don't raise the exception to prevent app startup failure
        pass
