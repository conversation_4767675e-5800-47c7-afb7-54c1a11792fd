from superset.utils.cache import cache
from superset.tenant_manager import tenant_manager

def make_tenant_cache_key(*args, **kwargs):
    """Create cache key with tenant prefix"""
    tenant_id = tenant_manager.get_current_tenant()
    base_key = cache.make_cache_key(*args, **kwargs)
    return f"tenant_{tenant_id}:{base_key}" if tenant_id else base_key

# Wrapper for tenant-aware caching
def tenant_cache_key(f):
    """Decorator to make cache keys tenant-aware"""
    def wrapper(*args, **kwargs):
        tenant_id = tenant_manager.get_current_tenant()
        if tenant_id:
            # Modify cache key to include tenant
            original_make_cache_key = cache.make_cache_key
            cache.make_cache_key = lambda *a, **k: f"tenant_{tenant_id}:{original_make_cache_key(*a, **k)}"
            try:
                return f(*args, **kwargs)
            finally:
                cache.make_cache_key = original_make_cache_key
        return f(*args, **kwargs)
    return wrapper