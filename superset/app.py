# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

import logging
import os
from typing import Optional

from flask import Flask

from superset.initialization import SupersetAppInitializer

# Optional prometheus support
try:
    from prometheus_client import make_wsgi_app
    from werkzeug.middleware.dispatcher import DispatcherMiddleware
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False

logger = logging.getLogger(__name__)


def _register_tenant_routes(app: Flask) -> None:
    """Register tenant-specific routes with Flask"""
    logger.info("Starting tenant route registration...")
    try:
        logger.info("Importing tenant functions...")
        from superset.tenant_routes import (
            tenant_home, tenant_dashboard_list, tenant_dashboard_view,
            tenant_chart_list, tenant_explore, tenant_sqllab,
            tenant_datasets, tenant_databases, tenant_catch_all
        )

        logger.info("Registering tenant routes directly with app...")

        # Register tenant routes directly with proper parameter passing
        app.add_url_rule('/tenant/<tenant_id>/', 'tenant_home', tenant_home, methods=['GET'])
        app.add_url_rule('/tenant/<tenant_id>/dashboard/', 'tenant_dashboard_list', tenant_dashboard_list, methods=['GET'])
        app.add_url_rule('/tenant/<tenant_id>/dashboard/list/', 'tenant_dashboard_list2', tenant_dashboard_list, methods=['GET'])
        app.add_url_rule('/tenant/<tenant_id>/superset/dashboard/<dashboard_id>/', 'tenant_dashboard_view', tenant_dashboard_view, methods=['GET'])
        app.add_url_rule('/tenant/<tenant_id>/chart/list/', 'tenant_chart_list', tenant_chart_list, methods=['GET'])
        app.add_url_rule('/tenant/<tenant_id>/explore/', 'tenant_explore', tenant_explore, methods=['GET'])
        app.add_url_rule('/tenant/<tenant_id>/sqllab/', 'tenant_sqllab', tenant_sqllab, methods=['GET'])
        app.add_url_rule('/tenant/<tenant_id>/tablemodelview/list/', 'tenant_datasets', tenant_datasets, methods=['GET'])
        app.add_url_rule('/tenant/<tenant_id>/databaseview/list/', 'tenant_databases', tenant_databases, methods=['GET'])
        app.add_url_rule('/tenant/<tenant_id>/<path:subpath>', 'tenant_catch_all', tenant_catch_all, methods=['GET'])

        logger.info("Tenant routes registered successfully")

        # Log registered routes for debugging
        for rule in app.url_map.iter_rules():
            if rule.rule.startswith('/tenant/'):
                logger.info(f"Registered tenant route: {rule.rule} -> {rule.endpoint}")

    except Exception as e:
        logger.error(f"Failed to register tenant routes: {e}")
        import traceback
        logger.error(traceback.format_exc())
        # Don't fail app startup for route registration issues
        pass


def _setup_tenant_metadata_database(app: Flask) -> None:
    """Setup tenant-aware metadata database switching"""
    logger.info("Setting up tenant-aware metadata database...")
    try:
        from superset.extensions import db
        from superset.tenant_database_engine import create_tenant_aware_engine

        # Get the original database URI
        original_uri = app.config['SQLALCHEMY_DATABASE_URI']
        logger.info(f"Original metadata database URI: {original_uri}")

        # Override the database engine creation to be tenant-aware
        # This is a bit of a hack, but it's the cleanest way to intercept metadata database connections
        original_get_engine = db.get_engine

        def tenant_aware_get_engine(app=None, bind=None):
            """Override get_engine to return tenant-aware engine"""
            try:
                from superset.tenant_manager import tenant_manager
                from flask import has_request_context

                # Only try tenant switching if we're in a request context
                if has_request_context():
                    tenant_id = tenant_manager.get_current_tenant()
                    if tenant_id:
                        tenant_uri = tenant_manager.get_tenant_database_uri(tenant_id)
                        if tenant_uri:
                            logger.info(f"🔄 USING TENANT METADATA DATABASE: {tenant_id} -> {tenant_uri}")
                            # Create a new engine for this tenant
                            return create_tenant_aware_engine(tenant_uri)

                # Fallback to original behavior
                return original_get_engine(app, bind)

            except Exception as e:
                logger.warning(f"Error in tenant-aware engine, using default: {e}")
                return original_get_engine(app, bind)

        # Replace the get_engine method
        db.get_engine = tenant_aware_get_engine
        logger.info("Tenant-aware metadata database setup complete")

    except Exception as e:
        logger.error(f"Failed to setup tenant-aware metadata database: {e}")
        # Don't fail app startup for this
        pass


def create_app(superset_config_module: Optional[str] = None) -> Flask:
    app = SupersetApp(__name__)

    try:
        # Allow user to override our config completely
        config_module = superset_config_module or os.environ.get(
            "SUPERSET_CONFIG", "superset.config"
        )
        app.config.from_object(config_module)

        # Add Prometheus metrics endpoint if available
        if PROMETHEUS_AVAILABLE:
            app.wsgi_app = DispatcherMiddleware(app.wsgi_app, {
                '/metrics': make_wsgi_app()
            })

        app_initializer = app.config.get("APP_INITIALIZER", SupersetAppInitializer)(app)
        app_initializer.init_app()

        # Initialize tenant system after app initialization
        # Only initialize if not already done (prevents duplicate initialization in dev mode)
        if not hasattr(app, '_tenant_system_initialized'):
            from superset.views.tenant_views import tenant_middleware
            from superset.utils.tenant_database_manager import initialize_tenant_databases

            # Initialize tenant middleware first
            tenant_middleware.init_app(app)

            # Initialize tenant databases (safe initialization that won't fail app startup)
            initialize_tenant_databases()

            # Setup tenant-aware metadata database
            _setup_tenant_metadata_database(app)

            # Register tenant routes
            _register_tenant_routes(app)

            # Mark as initialized to prevent duplicate initialization
            app._tenant_system_initialized = True
            logger.info("Tenant system and routes initialized successfully")

        return app

    # Make sure that bootstrap errors ALWAYS get logged
    except Exception as ex:
        logger.exception("Failed to create app")
        raise ex


class SupersetApp(Flask):
    pass
