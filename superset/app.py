# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

import logging
import os
from typing import Optional

from flask import Flask

from superset.initialization import SupersetAppInitializer
from prometheus_client import make_wsgi_app
from werkzeug.middleware.dispatcher import DispatcherMiddleware

logger = logging.getLogger(__name__)


def create_app(superset_config_module: Optional[str] = None) -> Flask:
    app = SupersetApp(__name__)

    try:
        # Allow user to override our config completely
        config_module = superset_config_module or os.environ.get(
            "SUPERSET_CONFIG", "superset.config"
        )
        app.config.from_object(config_module)

        # Add Prometheus metrics endpoint
        app.wsgi_app = DispatcherMiddleware(app.wsgi_app, {
            '/metrics': make_wsgi_app()
        })

        app_initializer = app.config.get("APP_INITIALIZER", SupersetAppInitializer)(app)
        app_initializer.init_app()

        # Initialize tenant system
        from superset.tenant_manager import tenant_manager
        from superset.views.tenant_views import tenant_middleware
        from superset.config import TENANT_DATABASES
        
        # Register tenant databases
        for tenant_id, db_uri in TENANT_DATABASES.items():
            tenant_manager.register_tenant_database(tenant_id, db_uri)
        
        # Initialize tenant middleware
        tenant_middleware.init_app(app)
        
        # Add custom URL routing for tenant-aware views
        @app.url_defaults
        def inject_tenant_id(endpoint, values):
            """Automatically inject tenant_id into URLs"""
            if 'tenant_id' not in values and tenant_manager.get_current_tenant():
                values['tenant_id'] = tenant_manager.get_current_tenant()
        
        @app.url_value_preprocessor
        def extract_tenant_id(endpoint, values):
            """Extract tenant_id from URL values"""
            if values and 'tenant_id' in values:
                tenant_id = values.pop('tenant_id')
                tenant_manager.set_current_tenant(tenant_id)

        return app

    # Make sure that bootstrap errors ALWAYS get logged
    except Exception as ex:
        logger.exception("Failed to create app")
        raise ex


class SupersetApp(Flask):
    pass
