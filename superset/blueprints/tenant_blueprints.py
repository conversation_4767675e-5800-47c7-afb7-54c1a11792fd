from flask import Blueprint, url_for as flask_url_for
from superset.tenant_manager import tenant_manager
import logging

class TenantBlueprint(Blueprint):
    """Blueprint that automatically prefixes URLs with tenant"""
    
    def __init__(self, name, import_name, **kwargs):
        # Add tenant prefix to URL prefix
        url_prefix = kwargs.get('url_prefix', '')
        kwargs['url_prefix'] = f'/tenant/<tenant_id>{url_prefix}'
        
        super().__init__(name, import_name, **kwargs)
        self.logger = logging.getLogger(__name__)
    
    def route(self, rule, **options):
        """Override route decorator to handle tenant context"""
        def decorator(f):
            def wrapper(*args, **kwargs):
                # Tenant ID will be automatically passed by Flask
                tenant_id = kwargs.get('tenant_id')
                if tenant_id:
                    tenant_manager.set_current_tenant(tenant_id)
                return f(*args, **kwargs)
            
            wrapper.__name__ = f.__name__
            return super(TenantBlueprint, self).route(rule, **options)(wrapper)
        return decorator

def tenant_url_for(endpoint, tenant_id=None, **values):
    """Generate URL with tenant prefix"""
    if not tenant_id:
        tenant_id = tenant_manager.get_current_tenant()
    
    if tenant_id and not endpoint.startswith('static'):
        values['tenant_id'] = tenant_id
    
    return flask_url_for(endpoint, **values)

# Monkey patch url_for to be tenant-aware
import superset.views
superset.views.url_for = tenant_url_for