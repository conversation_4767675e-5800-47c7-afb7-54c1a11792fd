from flask import Blueprint, request, redirect, url_for, abort, g, current_app
from flask_appbuilder import <PERSON>View, expose, has_access
from superset.tenant_manager import tenant_manager
import logging

class TenantMiddleware:
    """Middleware to handle tenant URL routing"""

    def __init__(self, app=None):
        self.app = app
        self.logger = logging.getLogger(__name__)
        if app:
            self.init_app(app)

    def init_app(self, app):
        """Initialize the middleware with Flask app"""
        app.before_request(self.before_request)
        app.url_map.converters['tenant'] = TenantConverter

        # Note: Tenant routes handle their own validation and context setting

    def before_request(self):
        """Extract tenant from URL before each request"""
        # Skip static files, API routes, and ALL tenant routes (let tenant routes handle their own validation)
        if (request.path.startswith('/static/') or
            request.path.startswith('/api/v1/') or
            request.path.startswith('/health') or
            request.path.startswith('/tenant/')):
            return

        # For non-tenant URLs, check if default tenant is configured
        default_tenant = current_app.config.get('DEFAULT_TENANT')
        if default_tenant and tenant_manager.validate_tenant(default_tenant):
            tenant_manager.set_current_tenant(default_tenant)
            self.logger.debug(f"Using default tenant: {default_tenant}")

class TenantConverter:
    """Custom URL converter for tenant validation"""
    
    def to_python(self, value):
        """Convert URL parameter to Python value"""
        if tenant_manager.validate_tenant(value):
            return value
        raise ValueError(f"Invalid tenant: {value}")
    
    def to_url(self, value):
        """Convert Python value to URL parameter"""
        return str(value)

class TenantBaseView(BaseView):
    """Base view class for tenant-aware views"""

    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)

    @property
    def current_tenant(self):
        """Get current tenant from context"""
        return tenant_manager.get_current_tenant()

    def tenant_required(self, f):
        """Decorator to ensure tenant context is available"""
        def wrapper(*args, **kwargs):
            if not self.current_tenant:
                abort(400, "Tenant context required")
            return f(*args, **kwargs)
        return wrapper

class TenantRoutingView(TenantBaseView):
    """View to handle tenant-specific routing"""

    def tenant_route(self, tenant_id, subpath=''):
        """Handle tenant-specific routes"""
        # Validate tenant
        if not tenant_manager.validate_tenant(tenant_id):
            abort(404, f"Tenant '{tenant_id}' not found")

        # Set tenant context
        tenant_manager.set_current_tenant(tenant_id)
        self.logger.info(f"Set tenant context to: {tenant_id}")

        # Default to welcome page if no subpath
        if not subpath:
            subpath = 'superset/welcome/'

        # Redirect to the actual route without tenant prefix
        target_url = f'/{subpath}'
        self.logger.info(f"Tenant {tenant_id} routing: /tenant/{tenant_id}/{subpath} -> {target_url}")

        return redirect(target_url)

    @expose('/tenant/<tenant_id>/')
    def tenant_home(self, tenant_id):
        """Handle tenant home route"""
        return self.tenant_route(tenant_id, '')

    @expose('/tenant/<tenant_id>/dashboard/')
    @expose('/tenant/<tenant_id>/dashboard/list/')
    def tenant_dashboard_list(self, tenant_id):
        """Handle tenant dashboard list route"""
        return self.tenant_route(tenant_id, 'dashboard/list/')

    @expose('/tenant/<tenant_id>/superset/dashboard/<dashboard_id>/')
    def tenant_dashboard_view(self, tenant_id, dashboard_id):
        """Handle tenant dashboard view route"""
        return self.tenant_route(tenant_id, f'superset/dashboard/{dashboard_id}/')

    @expose('/tenant/<tenant_id>/chart/list/')
    def tenant_chart_list(self, tenant_id):
        """Handle tenant chart list route"""
        return self.tenant_route(tenant_id, 'chart/list/')

    @expose('/tenant/<tenant_id>/explore/')
    def tenant_explore(self, tenant_id):
        """Handle tenant explore route"""
        return self.tenant_route(tenant_id, 'explore/')

    @expose('/tenant/<tenant_id>/sqllab/')
    def tenant_sqllab(self, tenant_id):
        """Handle tenant SQL Lab route"""
        return self.tenant_route(tenant_id, 'sqllab/')

    @expose('/tenant/<tenant_id>/<path:subpath>')
    def tenant_catch_all(self, tenant_id, subpath):
        """Handle all other tenant routes"""
        return self.tenant_route(tenant_id, subpath)

# Create tenant middleware instance
tenant_middleware = TenantMiddleware()