from flask import Blueprint, request, redirect, url_for, abort, g, current_app
from flask_appbuilder import BaseView, expose, has_access
from superset.tenant_manager import tenant_manager
import logging

class TenantMiddleware:
    """Middleware to handle tenant URL routing"""

    def __init__(self, app=None):
        self.app = app
        self.logger = logging.getLogger(__name__)
        if app:
            self.init_app(app)

    def init_app(self, app):
        """Initialize the middleware with Flask app"""
        app.before_request(self.before_request)
        app.url_map.converters['tenant'] = TenantConverter

        # Add URL routing helpers
        @app.url_defaults
        def inject_tenant_id(endpoint, values):
            """Automatically inject tenant_id into URLs for tenant routes"""
            if endpoint and endpoint.startswith('tenant.'):
                current_tenant = tenant_manager.get_current_tenant()
                if current_tenant and 'tenant_id' not in values:
                    values['tenant_id'] = current_tenant

        @app.url_value_preprocessor
        def extract_tenant_id(endpoint, values):
            """Extract tenant_id from URL values for tenant routes"""
            if values and 'tenant_id' in values:
                tenant_id = values.pop('tenant_id')
                if tenant_manager.validate_tenant(tenant_id):
                    tenant_manager.set_current_tenant(tenant_id)
                else:
                    abort(404, f"Tenant '{tenant_id}' not found")

    def before_request(self):
        """Extract tenant from URL before each request"""
        # Skip static files and non-tenant routes
        if (request.path.startswith('/static/') or
            request.path.startswith('/api/v1/') or
            request.path.startswith('/health')):
            return

        if request.path.startswith('/tenant/'):
            tenant_id = tenant_manager.extract_tenant_from_url(request.path)

            if tenant_id:
                # Validate tenant exists
                if not tenant_manager.validate_tenant(tenant_id):
                    self.logger.warning(f"Invalid tenant in URL: {tenant_id}")
                    abort(404, f"Tenant '{tenant_id}' not found")

                # Set tenant context
                try:
                    tenant_manager.set_current_tenant(tenant_id)
                    self.logger.debug(f"Request for tenant: {tenant_id}, path: {request.path}")
                except ValueError as e:
                    self.logger.error(f"Failed to set tenant context: {e}")
                    abort(400, str(e))
            else:
                self.logger.warning(f"Malformed tenant URL: {request.path}")
                abort(400, "Invalid tenant URL format")
        else:
            # For non-tenant URLs, check if default tenant is configured
            default_tenant = current_app.config.get('DEFAULT_TENANT')
            if default_tenant and tenant_manager.validate_tenant(default_tenant):
                tenant_manager.set_current_tenant(default_tenant)
                self.logger.debug(f"Using default tenant: {default_tenant}")

class TenantConverter:
    """Custom URL converter for tenant validation"""
    
    def to_python(self, value):
        """Convert URL parameter to Python value"""
        if tenant_manager.validate_tenant(value):
            return value
        raise ValueError(f"Invalid tenant: {value}")
    
    def to_url(self, value):
        """Convert Python value to URL parameter"""
        return str(value)

class TenantBaseView(BaseView):
    """Base view class for tenant-aware views"""
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)
    
    @property
    def current_tenant(self):
        """Get current tenant from context"""
        return tenant_manager.get_current_tenant()
    
    def tenant_required(self, f):
        """Decorator to ensure tenant context is available"""
        def wrapper(*args, **kwargs):
            if not self.current_tenant:
                abort(400, "Tenant context required")
            return f(*args, **kwargs)
        return wrapper

# Create tenant middleware instance
tenant_middleware = TenantMiddleware()