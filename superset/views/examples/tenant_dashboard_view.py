from flask import render_template, request, jsonify
from flask_appbuilder import BaseView, expose, has_access
from superset.views.tenant_views import TenantBaseView
from superset.tenant_manager import tenant_manager

class TenantDashboardView(TenantBaseView):
    """Example tenant-aware dashboard view"""
    
    default_view = 'dashboard'
    
    @expose('/dashboard/')
    @has_access
    def dashboard(self):
        """Main dashboard view for tenant"""
        tenant_id = self.current_tenant
        
        if not tenant_id:
            return "Tenant context required", 400
        
        # Your dashboard logic here
        # This will automatically use the tenant's database
        context = {
            'tenant_id': tenant_id,
            'tenant_name': tenant_id.title(),
            'database_uri': tenant_manager.get_tenant_database_uri(tenant_id)
        }
        
        return render_template('tenant_dashboard.html', **context)
    
    @expose('/api/data/')
    @has_access
    def get_tenant_data(self):
        """API endpoint to get tenant-specific data"""
        tenant_id = self.current_tenant
        
        if not tenant_id:
            return jsonify({'error': 'Tenant context required'}), 400
        
        # Execute query on tenant database
        engine = tenant_manager.get_tenant_engine(tenant_id)
        
        try:
            # Example query - replace with your actual queries
            with engine.connect() as conn:
                result = conn.execute("SELECT COUNT(*) as count FROM your_table")
                data = {'tenant': tenant_id, 'count': result.fetchone()[0]}
                return jsonify(data)
        except Exception as e:
            return jsonify({'error': str(e)}), 500

# Register the view
appbuilder.add_view(
    TenantDashboardView,
    "Dashboard",
    href="/tenant/<tenant_id>/dashboard/",
    category="Tenant"
)