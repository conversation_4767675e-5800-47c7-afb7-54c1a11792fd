"""
Tenant Routes - Simple Flask routes for tenant handling
"""
import logging
from flask import redirect, abort, request

logger = logging.getLogger(__name__)

def tenant_home(tenant_id):
    """Handle tenant home route"""
    return _handle_tenant_route(tenant_id, 'superset/welcome/')

def tenant_dashboard_list(tenant_id):
    """Handle tenant dashboard list route"""
    return _handle_tenant_route(tenant_id, 'dashboard/list/')

def tenant_dashboard_view(tenant_id, dashboard_id):
    """Handle tenant dashboard view route"""
    return _handle_tenant_route(tenant_id, f'superset/dashboard/{dashboard_id}/')

def tenant_chart_list(tenant_id):
    """Handle tenant chart list route"""
    return _handle_tenant_route(tenant_id, 'chart/list/')

def tenant_explore(tenant_id):
    """Handle tenant explore route"""
    return _handle_tenant_route(tenant_id, 'explore/')

def tenant_sqllab(tenant_id):
    """Handle tenant SQL Lab route"""
    return _handle_tenant_route(tenant_id, 'sqllab/')

def tenant_datasets(tenant_id):
    """Handle tenant datasets route"""
    return _handle_tenant_route(tenant_id, 'tablemodelview/list/')

def tenant_databases(tenant_id):
    """Handle tenant databases route"""
    return _handle_tenant_route(tenant_id, 'databaseview/list/')

def tenant_catch_all(tenant_id, subpath):
    """Handle all other tenant routes"""
    return _handle_tenant_route(tenant_id, subpath)

def _handle_tenant_route(tenant_id, subpath=''):
    """Handle tenant-specific routes"""
    # Import tenant_manager inside function to avoid app context issues
    from superset.tenant_manager import tenant_manager
    
    # Validate tenant
    if not tenant_manager.validate_tenant(tenant_id):
        logger.warning(f"Invalid tenant in URL: {tenant_id}")
        abort(404, f"Tenant '{tenant_id}' not found")
    
    # Set tenant context
    tenant_manager.set_current_tenant(tenant_id)
    logger.info(f"Set tenant context to: {tenant_id}")
    
    # Default to welcome page if no subpath
    if not subpath:
        subpath = 'superset/welcome/'
    
    # Redirect to the actual route without tenant prefix
    target_url = f'/{subpath}'
    
    # Preserve query parameters
    if request.args:
        query_string = request.query_string.decode('utf-8')
        target_url += f'?{query_string}'
    
    logger.info(f"Tenant {tenant_id} routing: /tenant/{tenant_id}/{subpath} -> {target_url}")
    
    return redirect(target_url)

# Note: Tenant context is set within each route function via _handle_tenant_route
