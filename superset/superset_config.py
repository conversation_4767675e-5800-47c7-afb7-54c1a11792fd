import logging
import os
from datetime import <PERSON><PERSON><PERSON>
from typing import Optional
from cachelib.file import FileSystemCache
from celery.schedules import crontab
import ssl

logger = logging.getLogger()
ssl._create_default_https_context = ssl._create_unverified_context

def get_env_variable(var_name: str, default: Optional[str] = None) -> str:
    """Get the environment variable or raise exception."""
    try:
        return os.environ[var_name]
    except KeyError:
        if default is not None:
            return default
        else:
            error_msg = "The environment variable {} was missing, abort...".format(
                var_name
            )
            raise EnvironmentError(error_msg)


DATABASE_DIALECT = get_env_variable("DATABASE_DIALECT")
DATABASE_USER = get_env_variable("DATABASE_USER")
DATABASE_PASSWORD = get_env_variable("DATABASE_PASSWORD")
DATABASE_HOST = get_env_variable("DATABASE_HOST")
DATABASE_PORT = get_env_variable("DATABASE_PORT")
DATABASE_DB = get_env_variable("DATABASE_DB")
TENANT_ID = get_env_variable("TENANT_ID")
SANDBOX_ID = get_env_variable("SANDBOX_ID")
REALM = get_env_variable("REALM")
CLIENT_ID = get_env_variable("CLIENT_ID")
CLIENT_SECRET = get_env_variable("CLIENT_SECRET")
SCOPE = get_env_variable("SCOPE")
API_URL = get_env_variable("API_URL")
TOKEN_URL = get_env_variable("TOKEN_URL")
AUTHORIZE_URL = get_env_variable("AUTHORIZE_URL")
SECRET_KEY = get_env_variable("SECRET_KEY")
DATA_DIR=get_env_variable("DATA_DIR")

# The SQLAlchemy connection string - this will be the default/fallback database
# Individual tenant databases will override this via the DB_CONNECTION_MUTATOR
SQLALCHEMY_DATABASE_URI = "%s://%s:%s@%s:%s/%s" % (
    DATABASE_DIALECT,
    DATABASE_USER,
    DATABASE_PASSWORD,
    DATABASE_HOST,
    DATABASE_PORT,
    DATABASE_DB,
)

REDIS_HOST = get_env_variable("REDIS_HOST")
REDIS_PORT = get_env_variable("REDIS_PORT")
REDIS_CELERY_DB = get_env_variable("REDIS_CELERY_DB", "0")
REDIS_RESULTS_DB = get_env_variable("REDIS_RESULTS_DB", "1")

RESULTS_BACKEND = FileSystemCache("/tmp/superset_home/sqllab")

CACHE_CONFIG = {
    "CACHE_TYPE": "redis",
    "CACHE_DEFAULT_TIMEOUT": 300,
    "CACHE_KEY_PREFIX": "superset_",
    "CACHE_REDIS_HOST": REDIS_HOST,
    "CACHE_REDIS_PORT": REDIS_PORT,
    "CACHE_REDIS_DB": REDIS_RESULTS_DB,
}
DATA_CACHE_CONFIG = CACHE_CONFIG

FILTER_STATE_CACHE_CONFIG = {
    'CACHE_TYPE': 'RedisCache',
    'CACHE_DEFAULT_TIMEOUT': 86400,
    'CACHE_KEY_PREFIX': 'superset_filter_cache',
    'CACHE_REDIS_URL': f'redis://{REDIS_HOST}:{REDIS_PORT}/0'
}
DATA_CACHE_CONFIG = {
    "CACHE_TYPE": "SupersetMetastoreCache",
    "CACHE_KEY_PREFIX": "superset_results",  # make sure this string is unique to avoid collisions
    "CACHE_DEFAULT_TIMEOUT": 86400,  # 60 seconds * 60 minutes * 24 hours
}
class CeleryConfig:
    broker_url = f"redis://{REDIS_HOST}:{REDIS_PORT}/0"
    imports = (
        "superset.sql_lab",
        "superset.tasks.scheduler",
    )
    result_backend = f"redis://{REDIS_HOST}:{REDIS_PORT}/0"
    worker_prefetch_multiplier = 10
    task_acks_late = True
    task_annotations = {
        "sql_lab.get_sql_results": {
            "rate_limit": "100/s",
        },
    }
    beat_schedule = {
        "reports.scheduler": {
            "task": "reports.scheduler",
            "schedule": crontab(minute="*", hour="*"),
        },
        "reports.prune_log": {
            "task": "reports.prune_log",
            "schedule": crontab(minute=0, hour=0),
        },
    }


CELERY_CONFIG = CeleryConfig

WEBDRIVER_BASEURL = get_env_variable("WEBDRIVER_BASEURL")
WEBDRIVER_BASEURL_USER_FRIENDLY = get_env_variable("WEBDRIVER_BASEURL_USER_FRIENDLY")
SCREENSHOT_LOCATE_WAIT = int(get_env_variable("SCREENSHOT_LOCATE_WAIT", default="100"))
SCREENSHOT_LOAD_WAIT = int(get_env_variable("SCREENSHOT_LOAD_WAIT", default="200"))

FEATURE_FLAGS = {
    'ALERT_REPORTS': True,
    "ALERTS_ATTACH_REPORTS": True,
}

EMAIL_PAGE_RENDER_WAIT = 60

# SMTP configuration
SMTP_HOST = get_env_variable('SMTP_HOST')
SMTP_PORT = get_env_variable('SMTP_PORT')
SMTP_STARTTLS = False
SMTP_SSL = True
SMTP_MAIL_FROM = get_env_variable('SMTP_MAIL_FROM')
SMTP_USER = get_env_variable('SMTP_USER')
SMTP_PASSWORD = get_env_variable('SMTP_PASSWORD')

ALERT_REPORTS_NOTIFICATION_DRY_RUN = False
ENABLE_ALERTS = True
CELERY_TASK_TRACK_STARTED = True
CELERY_BROKER_CONNECTION_MAX_RETRIES = 100
CELERY_TASK_TIME_LIMIT = 1200
CELERY_TASK_SOFT_TIME_LIMIT = 600 
ASYNC_TASK_EXECUTION=True

SQLLAB_CTAS_NO_LIMIT = True
APP_ICON = "/static/assets/images/zeta-logo.png"

from flask_appbuilder.security.manager import AUTH_OAUTH
# Set the authentication type to OAuth
AUTH_TYPE = AUTH_OAUTH

OAUTH_PROVIDERS = [
    {   'name':'cipher',
        'token_key':'access_token', # Name of the token in the response of access_token_url
        'icon':'fa-address-card',   # Icon for the provider
        'remote_app': {
            'client_id':f'{CLIENT_ID}',  # Client Id (Identify Superset application)
            'client_secret':f'{CLIENT_SECRET}', # Secret for this Client Id (Identify Superset application)
            'client_kwargs':{
                'scope': f'{SCOPE}',               # Scope for the Authorization
                'token_endpoint_auth_method': 'client_secret_basic',
                'token_placement': 'header',
                'verify': False
            },
            'access_token_method':'POST',    # HTTP Method to call access_token_url
            'access_token_params':{        # Additional parameters for calls to access_token_url
                'client_id':f'{CLIENT_ID}',
                'client_secret':f'{CLIENT_SECRET}'
            },
            'authorize_params': {
                'tenantId': f'{TENANT_ID}',
                'sandboxId': f'{SANDBOX_ID}',
                'domainId': f'{REALM}'
            },
            'api_base_url':f'{API_URL}',
            'access_token_url':f'{TOKEN_URL}token',
            'authorize_url':f'{AUTHORIZE_URL}'
        }
    }
]

# Will allow user self registration, allowing to create Flask users from Authorized User
AUTH_USER_REGISTRATION = True
ENABLE_PROXY_FIX=True

CORS_OPTIONS = {
    'supports_credentials': True,
    'allow_headers': ['*'],
    'resources': ['*'],
    'origins': ['*']
}
# The default user self registration role
AUTH_USER_REGISTRATION_ROLE = "sql_lab"

SESSION_COOKIE_SAMESITE = 'None'
SESSION_COOKIE_SECURE = False
SESSION_COOKIE_HTTPONLY = False
WTF_CSRF_ENABLED = False
SESSION_COOKIE_HTTPONLY = True
WTF_CSRF_TIME_LIMIT = int(timedelta(minutes=60).total_seconds())

PERMANENT_SESSION_LIFETIME_IN_MINUTES = int(get_env_variable("PERMANENT_SESSION_LIFETIME", default="360"))
PERMANENT_SESSION_LIFETIME = int(timedelta(minutes=PERMANENT_SESSION_LIFETIME_IN_MINUTES).total_seconds())

# Enable Flask-Talisman with custom configuration
TALISMAN_ENABLED = True
TALISMAN_CONFIG = {
    "force_https": False,
    "content_security_policy": {
        "default-src": ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
        "img-src": ["'self'", "data:"],
        "worker-src": ["'self'", "blob:"],
        "connect-src": ["'self'", "*"],
        "object-src": "'none'",
    }
}

from superset.security import SupersetSecurityManager

ADDITIONAL_SQL_LAB_PERMISSIONS = {
    ("can_activate", "TabStateView"),
    ("can_csv", "Superset"),
    ("can_delete_query", "TabStateView"),
    ("can_delete", "TabStateView"),
    ("can_execute_sql_query", "SQLLab"),
    ("can_export", "SavedQuery"),
    ("can_export_csv", "SQLLab"),
    ("can_get", "TabStateView"),
    ("can_get_results", "SQLLab"),
    ("can_migrate_query", "TabStateView"),
    ("can_sqllab", "Superset"),
    ("can_sqllab_history", "Superset"),
    ("can_put", "TabStateView"),
    ("can_post", "TabStateView"),
    ("can_write", "SavedQuery"),
    ("can_read", "Query"),
    ("can_read", "SQLLab"),
    ("can_read", "SavedQuery"),
    ("can_post", "TableSchemaView"),
    ("can_delete", "TableSchemaView"),
}

SupersetSecurityManager.SQLLAB_ONLY_PERMISSIONS.update(ADDITIONAL_SQL_LAB_PERMISSIONS)

from custom_sso_security_manager import CustomSsoSecurityManager
CUSTOM_SECURITY_MANAGER = CustomSsoSecurityManager

# Prometheus metrics - handle multiple initialization gracefully
try:
    from prometheus_client import Counter, Histogram, CollectorRegistry, REGISTRY

    # Check if metrics are already registered to avoid duplicates
    def get_or_create_counter(name, description, labels):
        try:
            return Counter(name, description, labels)
        except ValueError:
            # Metric already exists, find and return it
            for collector in list(REGISTRY._collector_to_names.keys()):
                if hasattr(collector, '_name') and collector._name == name:
                    return collector
            # If not found, create with a different registry
            return Counter(name, description, labels, registry=None)

    def get_or_create_histogram(name, description, labels, buckets):
        try:
            return Histogram(name, description, labels, buckets=buckets)
        except ValueError:
            # Metric already exists, find and return it
            for collector in list(REGISTRY._collector_to_names.keys()):
                if hasattr(collector, '_name') and collector._name == name:
                    return collector
            # If not found, create with a different registry
            return Histogram(name, description, labels, buckets=buckets, registry=None)

    SUP_QUERY_TOTAL = get_or_create_counter(
        'superset_sql_query_total',
        'Total number of SQL queries run',
        ['user', 'status']
    )

    SUP_QUERY_DURATION = get_or_create_histogram(
        'superset_sql_query_duration_seconds',
        'SQL query execution duration in seconds',
        ['user', 'datasource', 'query'],
        buckets=[3.0, 5.0, 10.0, 20.0, 30.0, 60.0, 90.0, 120.0, float("inf")]
    )

except ImportError:
    # Prometheus not available, create dummy objects
    class DummyMetric:
        def inc(self, *args, **kwargs):
            pass
        def observe(self, *args, **kwargs):
            pass
        def labels(self, *args, **kwargs):
            return self

    SUP_QUERY_TOTAL = DummyMetric()
    SUP_QUERY_DURATION = DummyMetric()

# Tenant configuration - moved to app initialization to avoid circular imports
def get_tenant_databases():
    """Build tenant database configuration from environment variables"""
    tenant_databases = {}

    # Individual tenant environment variables
    # Expected format: TENANT_<TENANT_ID>_HOST, TENANT_<TENANT_ID>_USER, etc.
    tenant_ids = get_env_variable("TENANT_IDS", "").split(",")

    for tenant_id in tenant_ids:
        if not tenant_id.strip():
            continue

        tenant_id = tenant_id.strip()
        try:
            # Get tenant-specific database configuration
            tenant_dialect = get_env_variable(f"TENANT_{tenant_id.upper()}_DIALECT", DATABASE_DIALECT)
            tenant_user = get_env_variable(f"TENANT_{tenant_id.upper()}_USER", DATABASE_USER)
            tenant_password = get_env_variable(f"TENANT_{tenant_id.upper()}_PASSWORD", DATABASE_PASSWORD)
            tenant_host = get_env_variable(f"TENANT_{tenant_id.upper()}_HOST", DATABASE_HOST)
            tenant_port = get_env_variable(f"TENANT_{tenant_id.upper()}_PORT", DATABASE_PORT)
            tenant_db = get_env_variable(f"TENANT_{tenant_id.upper()}_DATABASE")

            # Build connection string
            db_uri = f"{tenant_dialect}://{tenant_user}:{tenant_password}@{tenant_host}:{tenant_port}/{tenant_db}"
            tenant_databases[tenant_id] = db_uri

            logger.info(f"Configured tenant database: {tenant_id}")

        except EnvironmentError as e:
            logger.warning(f"Skipping tenant {tenant_id}: {e}")
            continue

    # Alternative: Direct URI environment variables
    # Expected format: TENANT_<TENANT_ID>_URI
    for key, value in os.environ.items():
        if key.startswith('TENANT_') and key.endswith('_URI'):
            tenant_id = key.replace('TENANT_', '').replace('_URI', '').lower()
            tenant_databases[tenant_id] = value
            logger.info(f"Configured tenant database from URI: {tenant_id}")

    return tenant_databases

# URL routing configuration
TENANT_URL_PREFIX = '/tenant'

# Optional: Default tenant for non-tenant URLs
DEFAULT_TENANT = get_env_variable("DEFAULT_TENANT", None)

# Database connection mutator for tenant-aware connections
def tenant_db_connection_mutator(sqlalchemy_uri, params, username, security_manager, source):
    """
    Mutate database connections based on tenant context.
    This function is called by Superset before creating database connections.
    """
    from superset.tenant_manager import tenant_manager

    # Get current tenant
    tenant_id = tenant_manager.get_current_tenant()

    if tenant_id:
        # Get tenant-specific database URI
        tenant_uri = tenant_manager.get_tenant_database_uri(tenant_id)
        if tenant_uri:
            logger.info(f"Using tenant database for {tenant_id}: {tenant_uri}")
            return tenant_uri, params

    # Return original URI if no tenant context or tenant not found
    return sqlalchemy_uri, params

# Set the database connection mutator
DB_CONNECTION_MUTATOR = tenant_db_connection_mutator