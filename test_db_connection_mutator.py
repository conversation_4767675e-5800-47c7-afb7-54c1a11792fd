#!/usr/bin/env python3
"""
Test script to verify database connection mutator is working
"""
import requests
import time
import sys

def test_db_connection_mutator():
    """Test if the database connection mutator is working"""
    print("🧪 Testing Database Connection Mutator")
    print("=" * 50)
    
    base_url = "http://localhost:8088"
    
    # Test 1: Check if Superset is running
    print("Testing Superset startup...")
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            print("✅ Superset is running")
        else:
            print(f"❌ Superset health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to Superset: {e}")
        return False
    
    # Test 2: Access tenant URL to trigger tenant context
    print("\nTesting tenant context setting...")
    tenant_urls = [
        f"{base_url}/tenant/tenant1/",
        f"{base_url}/tenant/tenant2/"
    ]
    
    for url in tenant_urls:
        try:
            response = requests.get(url, timeout=10, allow_redirects=False)
            if response.status_code == 302:
                redirect_url = response.headers.get('Location', '')
                print(f"✅ {url} -> {redirect_url}")
                
                # Check if tenant parameter is in redirect
                if 'tenant=' in redirect_url:
                    print(f"✅ Tenant parameter found in redirect")
                else:
                    print(f"❌ No tenant parameter in redirect")
            else:
                print(f"❌ Unexpected response: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ Error accessing {url}: {e}")
    
    # Test 3: Try to access a page that requires database access
    print("\nTesting database access pages...")
    db_pages = [
        f"{base_url}/superset/welcome/?tenant=tenant1",
        f"{base_url}/dashboard/list/?tenant=tenant1",
        f"{base_url}/api/v1/dashboard/?tenant=tenant1"
    ]
    
    for url in db_pages:
        try:
            response = requests.get(url, timeout=10)
            print(f"  {url} -> {response.status_code}")
            if response.status_code == 200:
                print(f"    ✅ Page loaded successfully")
            elif response.status_code == 401:
                print(f"    ⚠️  Authentication required (expected)")
            elif response.status_code == 403:
                print(f"    ⚠️  Access forbidden (expected)")
            else:
                print(f"    ❌ Unexpected status: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"    ❌ Error: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Database Connection Mutator Test Complete!")
    print("\nTo verify database switching:")
    print("1. Check Docker logs for 'DB_CONNECTION_MUTATOR called' messages")
    print("2. Look for 'SWITCHING DATABASE' messages")
    print("3. Check for tenant context debug messages")
    print("\nCommands to check logs:")
    print("  docker-compose logs superset-node | grep -E '(DB_CONNECTION_MUTATOR|SWITCHING|tenant)'")
    
    return True

if __name__ == "__main__":
    test_db_connection_mutator()
