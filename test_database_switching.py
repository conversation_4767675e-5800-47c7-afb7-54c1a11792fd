#!/usr/bin/env python3
"""
Test script to verify database switching by checking configured databases
"""
import requests
import json
import time

def test_database_switching():
    """Test database switching by checking configured databases"""
    print("🧪 Testing Database Switching")
    print("=" * 50)
    
    base_url = "http://localhost:8088"
    
    # Test 1: Check databases without tenant context
    print("1. Testing databases without tenant context...")
    try:
        response = requests.get(f"{base_url}/api/v1/database/", timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            databases = data.get('result', [])
            print(f"   Found {len(databases)} databases:")
            for db in databases:
                print(f"     - {db.get('database_name', 'Unknown')} (ID: {db.get('id')})")
        elif response.status_code == 401:
            print("   ⚠️  Authentication required")
        else:
            print(f"   ❌ Unexpected status: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Check databases with tenant1 context
    print("\n2. Testing databases with tenant1 context...")
    try:
        response = requests.get(f"{base_url}/api/v1/database/?tenant=tenant1", timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            databases = data.get('result', [])
            print(f"   Found {len(databases)} databases:")
            for db in databases:
                print(f"     - {db.get('database_name', 'Unknown')} (ID: {db.get('id')})")
        elif response.status_code == 401:
            print("   ⚠️  Authentication required")
        else:
            print(f"   ❌ Unexpected status: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Check databases with tenant2 context
    print("\n3. Testing databases with tenant2 context...")
    try:
        response = requests.get(f"{base_url}/api/v1/database/?tenant=tenant2", timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            databases = data.get('result', [])
            print(f"   Found {len(databases)} databases:")
            for db in databases:
                print(f"     - {db.get('database_name', 'Unknown')} (ID: {db.get('id')})")
        elif response.status_code == 401:
            print("   ⚠️  Authentication required")
        else:
            print(f"   ❌ Unexpected status: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 4: Check if we can access tenant URLs directly
    print("\n4. Testing direct tenant URL access...")
    tenant_urls = [
        f"{base_url}/tenant/tenant1/",
        f"{base_url}/tenant/tenant2/"
    ]
    
    for url in tenant_urls:
        try:
            response = requests.get(url, timeout=10, allow_redirects=True)
            print(f"   {url} -> {response.status_code}")
            if response.status_code == 200:
                # Check if the page contains tenant-specific content
                content = response.text
                if 'tenant1' in content.lower() or 'tenant2' in content.lower():
                    print(f"     ✅ Page contains tenant-specific content")
                else:
                    print(f"     ⚠️  No obvious tenant-specific content found")
            else:
                print(f"     ❌ Unexpected status: {response.status_code}")
        except Exception as e:
            print(f"     ❌ Error: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Database Switching Test Complete!")
    print("\nKey Points:")
    print("1. DB_CONNECTION_MUTATOR is called when Superset connects to DATA SOURCES")
    print("2. It's NOT called for Superset's internal metadata database")
    print("3. To test it properly, we need to:")
    print("   - Create data sources (databases) in Superset")
    print("   - Run queries against those data sources")
    print("   - Check if different tenants see different data")
    print("\nNext Steps:")
    print("1. Login to Superset UI")
    print("2. Create a database connection")
    print("3. Run a query to trigger DB_CONNECTION_MUTATOR")
    
    return True

if __name__ == "__main__":
    test_database_switching()
